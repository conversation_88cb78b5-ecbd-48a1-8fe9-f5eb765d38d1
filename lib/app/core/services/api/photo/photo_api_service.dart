import 'dart:convert';
import 'dart:typed_data';

import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:intl/intl.dart';

import '../../../models/item/item_model.dart';
import 'photo_api_methods.dart';

class PhotoApi {
  final http.Client _client;

  PhotoApi(this._client);

  Future<ItemModel?> photoUpload(
    List<Uint8List> images,
    String mainID,
    String owner,
    int? area,
    int? job,
    String? comment,
  ) async {
    var url = PhotoAPIMethod.photoUpload.url;

    var newFormatter = DateFormat("yyyyMMddHHmmss");
    var dateStr = newFormatter.format(DateTime.now());

    var request = http.MultipartRequest("POST", url);

    for (var i = 0; i < images.length; i++) {
      var content = http.MultipartFile.fromBytes("${i + 1}", images[i],
          filename: "${dateStr}_${i + 1}.jpg",
          contentType: MediaType('image', 'jpeg'));

      request.files.add(content);
    }

    request.fields['mainID'] = mainID;
    request.fields['count'] = "${images.length}";
    request.fields['owner'] = owner;

    if (comment?.isNotEmpty == true) {
      request.fields['comment'] = comment!;
    }

    if (area != null) {
      request.fields['area'] = "$area";
    }

    if (job != null) {
      request.fields['job'] = "$job";
    }

    var streamedResponse = await _client.send(request);
    var response = await http.Response.fromStream(streamedResponse);

    if (response.statusCode == 200) {
      final parsed = jsonDecode(response.body);
      final item = ItemModel.fromJson(parsed);
      return item;
    } else {
      //print(response);
      return null;
    }
  }
}
