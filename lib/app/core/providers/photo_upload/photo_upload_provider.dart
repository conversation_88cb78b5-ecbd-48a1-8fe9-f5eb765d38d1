import 'dart:typed_data';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../helpers/logger.dart';
import '../../models/item/item_model.dart';
import '../../services/api/service_provider.dart';
import '../common/common_providers.dart';
import '../notifier_mouted.dart';
import '../states/user/user_state.dart';
import '../update_providers.dart';

part 'photo_upload_provider.g.dart';

@riverpod
class PhotoUploadController extends _$PhotoUploadController
    with NotifierMounted {
  @override
  ItemModel build(ItemModel item) {
    ref.onDispose(setUnmounted);

    return item;
  }

  Future<bool> addNewPhoto(List<Uint8List> images, bool receiving) async {
    var mainID = item.mainID;

    if (mainID == null) {
      return false;
    }

    var area = ref.read(currentUserAreaProvider);
    var user = ref
        .read(currentUserProvider)
        .maybeWhen(auth: (user) => user, orElse: () {});

    var userLogin = user?.login ?? "Администратором";
    var comment = "";

    if ((area?.identifier ?? 0) > 0) {
      comment = area!.name;
    } else {
      comment = "Добавлено: $userLogin";
    }

    var areaID = area?.identifier ?? 0;
    var owner = user?.login ?? "";

    var jobID = receiving ? 1 : null;

    var photoProvider = ref.read(photoRepositoryProvider);

    var itemNew = await photoProvider.photoUpload(
      images,
      mainID,
      owner,
      areaID,
      jobID,
      comment,
    );

    if (itemNew == null) {
      //showInSnackBar("Ошибка загрузки фото", context);
      // await AppMetrica.reportError(
      //   message: 'Ошибка добавления фото',
      //   errorDescription: AppMetricaErrorDescription(
      //     StackTrace.current,
      //   ),
      // );
      logger.e("Error! PhotoUpload Error", error: 'Test Error');
      return false;
    }

    updateItemInProvidersWithRef(itemNew, ref);

    state = itemNew;
    return true;
  }
}
