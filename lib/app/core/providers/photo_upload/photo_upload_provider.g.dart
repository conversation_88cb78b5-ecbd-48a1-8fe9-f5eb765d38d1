// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'photo_upload_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$photoUploadControllerHash() =>
    r'65a368ee655c741638dafb7efdfdd80737b77db3';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$PhotoUploadController
    extends BuildlessAutoDisposeNotifier<ItemModel> {
  late final ItemModel item;

  ItemModel build(
    ItemModel item,
  );
}

/// See also [PhotoUploadController].
@ProviderFor(PhotoUploadController)
const photoUploadControllerProvider = PhotoUploadControllerFamily();

/// See also [PhotoUploadController].
class PhotoUploadControllerFamily extends Family<ItemModel> {
  /// See also [PhotoUploadController].
  const PhotoUploadControllerFamily();

  /// See also [PhotoUploadController].
  PhotoUploadControllerProvider call(
    ItemModel item,
  ) {
    return PhotoUploadControllerProvider(
      item,
    );
  }

  @override
  PhotoUploadControllerProvider getProviderOverride(
    covariant PhotoUploadControllerProvider provider,
  ) {
    return call(
      provider.item,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'photoUploadControllerProvider';
}

/// See also [PhotoUploadController].
class PhotoUploadControllerProvider
    extends AutoDisposeNotifierProviderImpl<PhotoUploadController, ItemModel> {
  /// See also [PhotoUploadController].
  PhotoUploadControllerProvider(
    ItemModel item,
  ) : this._internal(
          () => PhotoUploadController()..item = item,
          from: photoUploadControllerProvider,
          name: r'photoUploadControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$photoUploadControllerHash,
          dependencies: PhotoUploadControllerFamily._dependencies,
          allTransitiveDependencies:
              PhotoUploadControllerFamily._allTransitiveDependencies,
          item: item,
        );

  PhotoUploadControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.item,
  }) : super.internal();

  final ItemModel item;

  @override
  ItemModel runNotifierBuild(
    covariant PhotoUploadController notifier,
  ) {
    return notifier.build(
      item,
    );
  }

  @override
  Override overrideWith(PhotoUploadController Function() create) {
    return ProviderOverride(
      origin: this,
      override: PhotoUploadControllerProvider._internal(
        () => create()..item = item,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        item: item,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<PhotoUploadController, ItemModel>
      createElement() {
    return _PhotoUploadControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PhotoUploadControllerProvider && other.item == item;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, item.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PhotoUploadControllerRef on AutoDisposeNotifierProviderRef<ItemModel> {
  /// The parameter `item` of this provider.
  ItemModel get item;
}

class _PhotoUploadControllerProviderElement
    extends AutoDisposeNotifierProviderElement<PhotoUploadController, ItemModel>
    with PhotoUploadControllerRef {
  _PhotoUploadControllerProviderElement(super.provider);

  @override
  ItemModel get item => (origin as PhotoUploadControllerProvider).item;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
