// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'photo_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PhotoModelImpl _$$PhotoModelImplFromJson(Map<String, dynamic> json) =>
    _$PhotoModelImpl(
      url: json['url'] as String,
      owner: json['owner'] as String,
      area: (json['area'] as num?)?.toInt(),
      job: (json['job'] as num?)?.toInt(),
      comment: json['comment'] as String?,
      createdAt: (json['createdAt'] as num?)?.toDouble(),
      updatedAt: (json['updatedAt'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$PhotoModelImplToJson(_$PhotoModelImpl instance) =>
    <String, dynamic>{
      'url': instance.url,
      'owner': instance.owner,
      if (instance.area case final value?) 'area': value,
      if (instance.job case final value?) 'job': value,
      if (instance.comment case final value?) 'comment': value,
      if (instance.createdAt case final value?) 'createdAt': value,
      if (instance.updatedAt case final value?) 'updatedAt': value,
    };
