import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image_picker/image_picker.dart';
import 'package:universal_html/html.dart' as html;

import 'platform_check.dart';

typedef PhotoHelperResultCallback = Function(List<Uint8List>);

List<PopupMenuItem<int>> getMenuList(PhotoHelperResultCallback resultCallback) {
  List<PopupMenuItem<int>> menuList = [];

  if (stationComputer() == true) {
    menuList = [
      PopupMenuItem<int>(
          value: 1,
          child: const Text('Выбрать файл'),
          onTap: () async {
            var result = await showPickImage(ImageSource.gallery);
            resultCallback(result);
          }),
    ];
  } else {
    menuList = [
      PopupMenuItem<int>(
          value: 0,
          child: const Text('Открыть камеру'),
          onTap: () async {
            var result = await showPickImage(ImageSource.camera);
            resultCallback(result);
          }),
      PopupMenuItem<int>(
          value: 1,
          child: const Text('Открыть галерею'),
          onTap: () async {
            var result = await showPickImage(ImageSource.gallery);
            resultCallback(result);
          }),
    ];
  }

  return menuList;
}

Future<List<Uint8List>> showPickImage(ImageSource source) async {
  List<Uint8List> imagesArr = [];

  if (stationComputer() == true) {
    Future<FilePickerResult?> pickFiles() async {
      return FilePicker.platform.pickFiles(
        type: FileType.custom, // todo: test FileType.image
        withData: true,
        allowMultiple: true,
        allowedExtensions: ['jpg', 'png', 'jpeg'],
      );
    }

    FilePickerResult? result = await Future(pickFiles);

    for (PlatformFile image in result?.files ?? []) {
      if (image.bytes == null) {
        continue;
      }

      imagesArr.add(image.bytes!);
    }
  } else {
    final ImagePicker picker = ImagePicker();

    if (source == ImageSource.camera) {
      final XFile? image =
          await picker.pickImage(source: source, requestFullMetadata: false);

      if (image == null) {
        return [];
      }

      imagesArr.add(await image.readAsBytes());
    } else {
      final List<XFile> listImage = await picker.pickMultiImage();

      for (var image in listImage) {
        imagesArr.add(await image.readAsBytes());
      }
    }
  }

  //print(imagesArr.length);

  List<Uint8List> imagesNew = [];

  Future<Uint8List> compressImageForWeb(Uint8List imageData) async {
    final blob = html.Blob([imageData]);
    final url = html.Url.createObjectUrlFromBlob(blob);
    final img = html.ImageElement(src: url);

    await img.onLoad.first;

    final canvas = html.CanvasElement()
      ..width = 1920
      ..height = 1080;
    final ctx = canvas.getContext('2d') as html.CanvasRenderingContext2D;

    // Scale and draw the image
    ctx.drawImageScaled(img, 0, 0, 1920, 1080);

    // Convert to JPEG with quality
    final dataUrl = canvas.toDataUrl('image/jpeg', 0.7); // 70% quality
    final base64String = dataUrl;
    dataUrl.split(',')[1];
    final compressedData = base64Decode(base64String);

    html.Url.revokeObjectUrl(url);
    return Uint8List.fromList(compressedData);
  }

  if (kIsWeb) {
    for (var img in imagesArr) {
      final compressed = await compressImageForWeb(img);
      imagesNew.add(compressed);
    }
  } else {
    for (var tempImgData in imagesArr) {
      final image = await FlutterImageCompress.compressWithList(
        tempImgData,
        minHeight: 1920,
        minWidth: 1080,
        quality: 70,
      );
      imagesNew.add(image);
    }
  }

  return imagesNew;

  //addNewPhoto(imagesNew);
}
