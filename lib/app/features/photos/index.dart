import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/models/item/item_model.dart';
import 'package:uer_flutter/app/core/models/search/search_model.dart';
import 'package:uer_flutter/app/features/photos/index_controller.dart';
import 'package:uer_flutter/app/helpers/debouncer.dart';
import 'package:uer_flutter/app/routes/models/photo_detail_complex_data.dart';
import 'package:uer_flutter/app/routes/routes_name.dart';
import 'package:uer_flutter/app/styles/colors.dart';
import 'package:uer_flutter/app/styles/fonts.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/interactive/custom_card.dart';
import 'package:uer_flutter/app/widgets/search/search_field.dart';

class MainPhotosPage extends ConsumerStatefulWidget {
  const MainPhotosPage({super.key});

  @override
  ConsumerState<MainPhotosPage> createState() => _MainPhotosPageState();
}

class _MainPhotosPageState extends ConsumerState<MainPhotosPage> {
  SearchModel searchModel = const SearchModel();
  final _debouncer = Debouncer(milliseconds: 650);
  TextEditingController searchController = TextEditingController();
  bool searchByOrder = true;

  @override
  void initState() {
    super.initState();
  }

  void _updateSearchTerm(SearchModel? newSearchModel) {
    _debouncer.run(() {
      if (newSearchModel == null) {
        setState(() {
          searchModel = const SearchModel();
        });
        return;
      } else {
        searchModel = newSearchModel;
      }

      ref.read(photosControllerProvider.notifier).fetchItems(searchModel);
    });
  }

  Future<void> _refresh() async {
    await ref.read(photosControllerProvider.notifier).fetchItems(searchModel);
  }

  @override
  Widget build(BuildContext context) {
    final photos = ref.watch(photosControllerProvider);

    return Scaffold(
        appBar: CustomAppBar(text: 'Фото'),
        body: Column(children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0.0),
            child: Row(
              children: [
                SearchField(
                  hint: searchByOrder
                      ? 'Поиск по оборудованию'
                      : 'Поиск по ремонтному номеру',
                  controller: searchController,
                  callback: (text) {
                    _updateSearchTerm(SearchModel(
                      repairNumber: searchByOrder ? null : text,
                      searchEquipment: searchByOrder ? text : null,
                    ));
                  },
                ),
                SizedBox(width: 12.0),
                TextButton(
                  onPressed: () {
                    setState(() {
                      searchByOrder = !searchByOrder;
                    });
                  },
                  child: Text(
                    searchByOrder
                        ? 'Искать по рем. номеру'
                        : 'Искать по оборудованию',
                    style: AppFonts.labelMedium,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: photos.when(data: (data) {
              return RefreshIndicator.adaptive(
                onRefresh: _refresh,
                child: ListView.builder(
                  padding: EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: data.length,
                  itemBuilder: (BuildContext context, int index) {
                    if (data.isEmpty) {
                      return const Center(
                        child: Text('Нет данных'),
                      );
                    }
                    final item = data[index];

                    return OrderCard(item: item);
                  },
                ),
              );
            }, error: (error, stackTrace) {
              print(stackTrace);
              return Center(
                child: Text(error.toString()),
              );
            }, loading: () {
              return const Center(child: CircularProgressIndicator());
            }),
          )
        ]));
  }
}

class OrderCard extends StatefulWidget {
  const OrderCard({
    super.key,
    required this.item,
  });

  final ItemModel item;

  @override
  State<OrderCard> createState() => _OrderCardState();
}

class _OrderCardState extends State<OrderCard> {
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 12.0),
      child: CustomCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Wrap(
                    crossAxisAlignment: WrapCrossAlignment.center,
                    runSpacing: 6.0,
                    children: [
                      Text(
                        widget.item.repairNumber ?? '#',
                        style: AppFonts.labelLarge,
                      ),
                      SizedBox(
                          height: 20.0, child: VerticalDivider(width: 20.0)),
                      Text(
                        widget.item.order?.equipment ?? '#',
                        style: AppFonts.labelLarge,
                      ),
                      SizedBox(
                          height: 20.0, child: VerticalDivider(width: 20.0)),
                      Text(
                        widget.item.order?.name ?? '#',
                        style: AppFonts.labelLarge,
                      ),
                      SizedBox(
                          height: 20.0, child: VerticalDivider(width: 20.0)),
                      Text(
                        widget.item.order?.branch?.name ?? '#',
                        style: AppFonts.labelLarge,
                      ),
                      SizedBox(
                          height: 20.0, child: VerticalDivider(width: 20.0)),
                      Text(
                        widget.item.order?.client?.name ?? '#',
                        style: AppFonts.labelLarge,
                      ),
                    ],
                  ),
                ),
                Text(
                  widget.item.order?.date ?? '#',
                  style: AppFonts.labelMedium
                      .merge(TextStyle(color: AppLightColors.lightGray)),
                ),
              ],
            ),
            SizedBox(height: 28.0),
            Wrap(
              spacing: 6.0,
              runSpacing: 6.0,
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                ...widget.item.photos!
                    .asMap()
                    .entries
                    .take(_isExpanded ? widget.item.photos!.length : 7)
                    .map((entry) {
                  final photo = entry.value;
                  final index = entry.key;

                  return Container(
                    width: 100.0,
                    height: 100.0,
                    decoration: BoxDecoration(
                      color: AppLightColors.prepreWhite,
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(10.0),
                      clipBehavior: Clip.antiAlias,
                      child: InkWell(
                        onTap: () {
                          context.pushNamed(
                            RoutesName.photoDetail.named,
                            extra: PhotoDetailPageComplexData(
                              photos: widget.item.photos!,
                              initialIndex: index,
                            ),
                          );
                        },
                        borderRadius: BorderRadius.circular(10.0),
                        child: Ink.image(
                          image: NetworkImage(photo.getImgUrl()),
                          fit: BoxFit.cover,
                          width: 100.0,
                          height: 100.0,
                        ),
                      ),
                    ),
                  );
                }),
                if (widget.item.photos!.length > 5)
                  Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: TextButton(
                      onPressed: () {
                        setState(() {
                          _isExpanded = !_isExpanded;
                        });
                      },
                      child: Text(
                        _isExpanded
                            ? 'Скрыть'
                            : 'Показать все (${widget.item.photos!.length})',
                        style: AppFonts.labelMedium,
                      ),
                    ),
                  ),
                if (widget.item.photos == null || widget.item.photos!.isEmpty)
                  Text(
                    'Нет фото',
                    style: AppFonts.labelMedium.merge(
                      TextStyle(
                        color: AppLightColors.lightGray,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
