import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/models/item/item_model.dart';
import 'package:uer_flutter/app/core/models/search/search_model.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import 'package:uer_flutter/app/core/services/api/service_provider.dart';

part 'index_controller.g.dart';

@riverpod
class PhotosController extends _$PhotosController with NotifierMounted {
  @override
  FutureOr<List<ItemModel>> build() {
    ref.onDispose(setUnmounted);

    return fetchItems(null);
  }

  Future<List<ItemModel>> fetchItems(SearchModel? searchModel) async {
    SearchModel? search;

    if (searchModel == null) {
      search = SearchModel(projection: 'photos repairNumber order', limit: 10);
    } else {
      search = searchModel.copyWith(
        projection: 'photos repairNumber order',
        limit: 10,
      );
    }

    final apiClient = ref.read(itemRepositoryProvider);
    final items = await apiClient.getItems(search);

    // set state
    state = AsyncValue.data(items ?? []);

    return items ?? [];
  }
}
