import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uer_flutter/app/core/models/item/item_model.dart';
import 'package:uer_flutter/app/core/models/search/search_model.dart';
import 'package:uer_flutter/app/core/providers/notifier_mouted.dart';
import 'package:uer_flutter/app/core/services/api/service_provider.dart';

part 'index_controller.g.dart';

@riverpod
class PhotosController extends _$PhotosController with NotifierMounted {
  static const int _pageSize = 10;

  List<ItemModel> _allItems = [];
  SearchModel? _currentSearch;
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentOffset = 0;

  @override
  FutureOr<List<ItemModel>> build() {
    ref.onDispose(setUnmounted);

    return fetchItems(null);
  }

  bool get isLoading => _isLoading;
  bool get hasMore => _hasMore;
  List<ItemModel> get allItems => _allItems;

  Future<List<ItemModel>> fetchItems(SearchModel? searchModel,
      {bool reset = true}) async {
    if (_isLoading) return _allItems;

    _isLoading = true;

    if (reset) {
      _currentSearch = searchModel;
      _currentOffset = 0;
      _allItems.clear();
      _hasMore = true;
    } else {
      if (!_hasMore) return _allItems;
    }

    try {
      SearchModel search;

      if (searchModel == null && _currentSearch == null) {
        search = SearchModel(
          projection: 'photos repairNumber order',
          limit: _pageSize,
          offset: _currentOffset,
        );
      } else {
        final baseSearch = searchModel ?? _currentSearch!;
        search = baseSearch.copyWith(
          projection: 'photos repairNumber order',
          limit: _pageSize,
          offset: _currentOffset,
        );
      }

      final apiClient = ref.read(itemRepositoryProvider);
      final items = await apiClient.getItems(search);
      final newItems = items ?? [];

      _hasMore = newItems.length == _pageSize;

      if (reset) {
        _allItems = newItems;
      } else {
        _allItems.addAll(newItems);
      }

      _currentOffset += newItems.length;
      _isLoading = false;

      // Обновляем состояние
      state = AsyncValue.data(_allItems);

      return _allItems;
    } catch (error, stackTrace) {
      _isLoading = false;
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }

  Future<void> loadMore() async {
    await fetchItems(null, reset: false);
  }

  Future<void> refresh() async {
    await fetchItems(_currentSearch, reset: true);
  }
}
