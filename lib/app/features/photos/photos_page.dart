import 'dart:typed_data';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uer_flutter/app/core/models/photo/photo_model.dart';
import 'package:uer_flutter/app/helpers/date_extension.dart';
import 'package:uer_flutter/app/helpers/platform_check.dart';
import 'package:uer_flutter/app/routes/models/photo_detail_complex_data.dart';
import 'package:uer_flutter/app/widgets/bars/app_bar.dart';
import 'package:uer_flutter/app/widgets/share_button.dart';

import '../../core/models/item/item_model.dart';
import '../../core/providers/photo_upload/photo_upload_provider.dart';
import '../../helpers/photo_helpers.dart';
import '../../helpers/snack_bar.dart';
import '../../routes/routes_name.dart';

class PhotosPage extends ConsumerStatefulWidget {
  const PhotosPage({super.key, required this.item});

  final ItemModel item;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _PhotosPageState();
}

class _PhotosPageState extends ConsumerState<PhotosPage> {
  // handling select photos for download from app
  final List<PhotoModel> selectedPhotos = [];

  void selectPhoto(PhotoModel? photo) {
    if (photo == null) return;
    if (selectedPhotos.contains(photo)) {
      setState(() => selectedPhotos.remove(photo));
      return;
    }
    setState(() => selectedPhotos.add(photo));
  }

  void selectAllPhotos() {
    if (selectedPhotos.isNotEmpty) {
      setState(() => selectedPhotos.clear());
      return;
    }
    setState(() {
      selectedPhotos.addAll(widget.item.photos ?? []);
    });
  }

  void openDetailPhoto(index) {
    var item = ref.read(photoUploadControllerProvider(widget.item));
    var photos = item.photos?.reversed.toList();

    if (photos?.isNotEmpty == true) {
      context.pushNamed(
        RoutesName.photoDetail.named,
        extra: PhotoDetailPageComplexData(photos: photos!, initialIndex: index),
      );
    }
  }

  void uploadSelectedPhotos(List<Uint8List> selectedPhotos) async {
    if (selectedPhotos.isNotEmpty) {
      var controller = ref.read(
        photoUploadControllerProvider(widget.item).notifier,
      );

      controller.addNewPhoto(selectedPhotos, false);
    } else {
      showInSnackBar("Фото не выбраны", context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final item = ref.watch(photoUploadControllerProvider(widget.item));
    final photos = item.photos?.reversed.toList();
    final screenWidth = MediaQuery.sizeOf(context).width;

    return Scaffold(
      appBar: CustomAppBar(
        text: "Фото",
        actions: [
          ElevatedButton(
            onPressed: selectAllPhotos,
            child:
                Text(selectedPhotos.isEmpty ? 'Выбрать всё' : 'Исключить всё'),
          ),
          const SizedBox(width: 6.0),
          ShareBtn(photos: selectedPhotos),
          const SizedBox(width: 6.0),
          PopupMenuButton<int>(
            icon: const Icon(Icons.add_a_photo),
            tooltip: "Добавить фото",
            onSelected: (item) {},
            itemBuilder: (context) => getMenuList(uploadSelectedPhotos),
          ),
        ],
      ),
      body: GridView.builder(
          itemCount: photos?.length ?? 0,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: screenWidth > 812 ? 5 : 2,
          ),
          itemBuilder: (_, int index) {
            var photo = photos?[index];

            var dateStr = DateTime.fromMillisecondsSinceEpoch(
                    ((photo?.createdAt ?? 0).ceil() * 1000))
                .dateShortWithTimeString();

            CachedNetworkImage? cachedImg;

            if (isMacOS() || isAndroidOriOS() && photo != null) {
              cachedImg = CachedNetworkImage(
                imageUrl: photo!.getImgUrl(),
                progressIndicatorBuilder: (context, url, downloadProgress) =>
                    FittedBox(
                  fit: BoxFit.scaleDown,
                  child: CircularProgressIndicator(
                    value: downloadProgress.progress,
                  ),
                ),
                errorWidget: (context, url, error) => const Icon(Icons.error),
              );
            }

            return GridTile(
              child: Stack(
                fit: StackFit.expand,
                alignment: Alignment.center,
                children: [
                  InkWell(
                    onTap: () => openDetailPhoto(index),
                    child: Ink(
                      padding: const EdgeInsets.all(6.0),
                      child: Stack(
                        fit: StackFit.expand,
                        alignment: Alignment.center,
                        children: [
                          Hero(
                            tag: photo?.url ?? '',
                            child: cachedImg ??
                                Image.network(
                                  photo?.getImgUrl() ?? "",
                                  fit: BoxFit.contain,
                                ),
                          ),
                          Positioned(
                            bottom: 16.0,
                            left: 0,
                            right: 0,
                            child: Container(
                              alignment: Alignment.center,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8.0,
                              ),
                              color: Colors.white54,
                              child: Wrap(
                                alignment: WrapAlignment.center,
                                children: [
                                  Text(
                                    '${photo?.owner} | ',
                                    style: TextStyle(
                                      color: Colors.black,
                                      fontSize: screenWidth > 812 ? 16.0 : 12.0,
                                      fontWeight: FontWeight.w800,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  Text(
                                    dateStr,
                                    style: TextStyle(
                                      color: Colors.black,
                                      fontSize: screenWidth > 812 ? 16.0 : 12.0,
                                      fontWeight: FontWeight.w800,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                    left: 10.0,
                    top: 10.0,
                    child: Checkbox.adaptive(
                      value: selectedPhotos.contains(photo),
                      onChanged: (_) => selectPhoto(photo),
                    ),
                  ),
                ],
              ),
            );
          }),
    );
  }
}
