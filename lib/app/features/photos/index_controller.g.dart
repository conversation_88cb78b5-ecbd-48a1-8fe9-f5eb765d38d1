// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$photosControllerHash() => r'5d251bfa637201dff76ca2074d8d5c8d7105ccd5';

/// See also [PhotosController].
@ProviderFor(PhotosController)
final photosControllerProvider = AutoDisposeAsyncNotifierProvider<
    PhotosController, List<ItemModel>>.internal(
  PhotosController.new,
  name: r'photosControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$photosControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PhotosController = AutoDisposeAsyncNotifier<List<ItemModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
