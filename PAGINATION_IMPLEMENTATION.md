# Реализация пагинации (Infinity Scroll) для PhotosController

## Что было добавлено:

### 1. PhotosController (lib/app/features/photos/index_controller.dart)
- Добавлены поля для управления пагинацией:
  - `_allItems`: список всех загруженных элементов
  - `_currentSearch`: текущий поисковый запрос
  - `_isLoading`: флаг загрузки
  - `_hasMore`: флаг наличия дополнительных данных
  - `_currentOffset`: текущий offset для пагинации
  - `_pageSize`: размер страницы (10 элементов)

- Обновлен метод `fetchItems()`:
  - Поддержка параметра `reset` для сброса списка при новом поиске
  - Автоматическое управление offset
  - Определение наличия дополнительных данных

- Добавлены новые методы:
  - `loadMore()`: загрузка следующей страницы
  - `refresh()`: обновление текущих данных
  - Геттеры для доступа к состоянию пагинации

### 2. MainPhotosPage (lib/app/features/photos/index.dart)
- Добавлен `ScrollController` для отслеживания прокрутки
- Реализован метод `_onScroll()` для автоматической загрузки при достижении конца списка
- Обновлен ListView.builder:
  - Добавлен индикатор загрузки в конце списка
  - Поддержка динамического количества элементов
  - Интеграция с контроллером прокрутки

- Обновлены методы:
  - `_updateSearchTerm()`: использует параметр `reset: true`
  - `_refresh()`: использует новый метод `refresh()`

## Как это работает:

1. **Первоначальная загрузка**: При инициализации контроллера загружается первая страница (10 элементов)

2. **Поиск**: При вводе поискового запроса список сбрасывается и загружается заново

3. **Бесконечная прокрутка**: При прокрутке до конца списка (за 200px до конца) автоматически загружается следующая страница

4. **Индикатор загрузки**: Показывается в конце списка во время загрузки новых данных

5. **Обновление**: Pull-to-refresh обновляет весь список с текущими параметрами поиска

## Особенности реализации:

- Использует существующую структуру Riverpod без изменения типов
- Совместимо с текущим API
- Поддерживает все существующие функции поиска
- Оптимизировано для производительности (предотвращение дублирующих запросов)
- Автоматическое управление состоянием загрузки
